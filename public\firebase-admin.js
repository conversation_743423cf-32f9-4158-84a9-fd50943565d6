// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-app.js";
import { getFirestore, collection, getDocs, deleteDoc, doc, updateDoc, query, orderBy, onSnapshot, serverTimestamp } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-firestore.js";
import { getAuth, onAuthStateChanged, signOut } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-auth.js";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyA0tjUO8tJKGgDJFTWpoMKgQQqsdHYDeBE",
  authDomain: "print-design-pd.firebaseapp.com",
  projectId: "print-design-pd",
  storageBucket: "print-design-pd.firebasestorage.app",
  messagingSenderId: "678212447286",
  appId: "1:678212447286:web:81c6c1f16ebd599d68bc97",
  measurementId: "G-E98ZKDHSKK"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Global variables
let currentRequests = [];
let currentDeleteId = null;

// Make currentRequests globally accessible for debugging
window.currentRequests = currentRequests;

// Check authentication state
onAuthStateChanged(auth, (user) => {
    console.log("Auth state changed:", user ? "User signed in" : "User signed out");
    if (user) {
        console.log("User email:", user.email);
        // User is signed in, load data
        loadRequests();
        setupEventListeners();
    } else {
        console.log("Redirecting to login...");
        // User is signed out, redirect to login
        window.location.href = 'login.html';
    }
});

// Load requests from Firestore
async function loadRequests() {
    try {
        showLoadingIndicator();

        const q = query(collection(db, "contactos"), orderBy("timestamp", "desc"));

        onSnapshot(q, (querySnapshot) => {
            currentRequests = [];
            window.currentRequests = currentRequests;

            // Process each document
            querySnapshot.forEach((doc) => {
                const data = doc.data();

                // Ensure all required fields exist with defaults
                const request = {
                    id: doc.id,
                    nombre: data.nombre || 'Sin nombre',
                    email: data.email || 'Sin email',
                    telefono: data.telefono || 'Sin teléfono',
                    mensaje: data.mensaje || 'Sin mensaje',
                    timestamp: data.timestamp ? data.timestamp.toDate() : new Date(),
                    status: data.status || 'pending',
                    priority: data.priority || 'medium'
                };

                currentRequests.push(request);
                window.currentRequests = currentRequests;
            });

            // Update UI
            hideLoadingIndicator();
            updateDashboardStats();
            renderRequestsTable();

        }, (error) => {
            console.error("onSnapshot error:", error);
            hideLoadingIndicator();
            showError("Error al cargar los datos: " + error.message);
        });

    } catch (error) {
        console.error("Error loading requests:", error);
        hideLoadingIndicator();
        showError("Error al cargar los datos: " + error.message);
    }
}

// Loading indicator functions
function showLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    const requestsTable = document.getElementById('requests-table');

    if (loadingIndicator) loadingIndicator.style.display = 'flex';
    if (requestsTable) requestsTable.style.display = 'none';
}

function hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    const requestsTable = document.getElementById('requests-table');

    if (loadingIndicator) loadingIndicator.style.display = 'none';
    if (requestsTable) requestsTable.style.display = 'table';
}

// Update dashboard statistics
function updateDashboardStats() {
    const pending = currentRequests.filter(req => req.status === 'pending').length;
    const thisWeek = currentRequests.filter(req => {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return req.timestamp >= weekAgo;
    }).length;
    const completedMonth = currentRequests.filter(req => req.status === 'completed').length;
    const urgent = currentRequests.filter(req => req.priority === 'urgent').length;
    
    document.getElementById('pending-messages').textContent = pending;
    document.getElementById('this-week').textContent = thisWeek;
    document.getElementById('completed-month').textContent = completedMonth;
    document.getElementById('urgent').textContent = urgent;
}

// Render requests table
function renderRequestsTable() {
    const tbody = document.getElementById('requests-tbody');
    const statusFilter = document.getElementById('status-filter').value;
    const priorityFilter = document.getElementById('priority-filter').value;
    const searchTerm = document.getElementById('search-input').value.toLowerCase();

    // Filter requests
    let filteredRequests = currentRequests.filter(req => {
        const matchesStatus = !statusFilter || req.status === statusFilter;
        const matchesPriority = !priorityFilter || req.priority === priorityFilter;
        const matchesSearch = !searchTerm ||
            req.nombre.toLowerCase().includes(searchTerm) ||
            req.email.toLowerCase().includes(searchTerm);

        return matchesStatus && matchesPriority && matchesSearch;
    });
    
    // Clear table
    tbody.innerHTML = '';
    
    if (filteredRequests.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 2rem; color: #ccc;">
                    <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>No hay solicitudes que mostrar</p>
                    <small>Total de solicitudes cargadas: ${currentRequests.length}</small>
                    <br><small>Filtros aplicados: ${statusFilter || 'Ninguno'} | ${priorityFilter || 'Ninguna'} | "${searchTerm || 'Ninguna búsqueda'}"</small>
                </td>
            </tr>
        `;
        return;
    }
    
    // Add rows
    filteredRequests.forEach((request, index) => {
        
        const row = document.createElement('tr');
        row.setAttribute('data-request-id', request.id);
        
        // Ensure all fields have valid values
        const safeNombre = request.nombre || 'Sin nombre';
        const safeEmail = request.email || 'Sin email';
        const safeTelefono = request.telefono || 'Sin teléfono';
        const safeMensaje = request.mensaje || 'Sin mensaje';
        const safeStatus = request.status || 'pending';
        const safePriority = request.priority || 'medium';
        
        row.innerHTML = `
            <td>${request.id}</td>
            <td>${safeNombre}</td>
            <td>${safeEmail}</td>
            <td>${safeTelefono}</td>
            <td>${safeMensaje.length > 50 ? safeMensaje.substring(0, 50) + '...' : safeMensaje}</td>
            <td>
                <span class="status-badge status-${safeStatus}">${getStatusText(safeStatus)}</span>
                <select onchange="updateStatus('${request.id}', this.value)" class="status-select">
                    <option value="pending" ${safeStatus === 'pending' ? 'selected' : ''}>Pendiente</option>
                    <option value="in_progress" ${safeStatus === 'in_progress' ? 'selected' : ''}>En Progreso</option>
                    <option value="completed" ${safeStatus === 'completed' ? 'selected' : ''}>Completado</option>
                    <option value="cancelled" ${safeStatus === 'cancelled' ? 'selected' : ''}>Cancelado</option>
                </select>
            </td>
            <td>
                <span class="priority-badge priority-${safePriority}">${getPriorityText(safePriority)}</span>
                <select onchange="updatePriority('${request.id}', this.value)" class="priority-select">
                    <option value="low" ${safePriority === 'low' ? 'selected' : ''}>Baja</option>
                    <option value="medium" ${safePriority === 'medium' ? 'selected' : ''}>Media</option>
                    <option value="high" ${safePriority === 'high' ? 'selected' : ''}>Alta</option>
                    <option value="urgent" ${safePriority === 'urgent' ? 'selected' : ''}>Urgente</option>
                </select>
            </td>
            <td>${formatDate(request.timestamp)}</td>
            <td>
                <button class="btn-action btn-view" onclick="viewRequest('${request.id}')" title="Ver detalles">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-action btn-delete" onclick="deleteRequest('${request.id}')" title="Eliminar">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
        console.log(`Row ${index + 1} added for request ID:`, request.id);
    });
    
    console.log("Table rendering completed. Total rows:", tbody.children.length);
}

// Setup event listeners
function setupEventListeners() {
    // Filter controls
    document.getElementById('status-filter').addEventListener('change', renderRequestsTable);
    document.getElementById('priority-filter').addEventListener('change', renderRequestsTable);
    document.getElementById('search-input').addEventListener('input', renderRequestsTable);
    
    // Clear filters button
    document.getElementById('clear-filters').addEventListener('click', function() {
        document.getElementById('status-filter').value = '';
        document.getElementById('priority-filter').value = '';
        document.getElementById('search-input').value = '';
        renderRequestsTable();
    });
    
    // Logout button
    const logoutBtn = document.querySelector('a[href="login.html"]');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            try {
                await signOut(auth);
                window.location.href = 'login.html';
            } catch (error) {
                console.error("Logout error:", error);
            }
        });
    }

    // Funcionalidad del menú hamburguesa para el panel de administración
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const adminNav = document.querySelector('.admin-nav');

    // Toggle del menú móvil
    if (mobileMenuToggle && adminNav) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenuToggle.classList.toggle('active');
            adminNav.classList.toggle('active');
            document.body.classList.toggle('menu-open');
        });

        // Cerrar menú al hacer clic en un enlace
        const navLinks = adminNav.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenuToggle.classList.remove('active');
                adminNav.classList.remove('active');
                document.body.classList.remove('menu-open');
            });
        });



        // Cerrar menú al hacer clic fuera del menú
        document.addEventListener('click', function(e) {
            if (!mobileMenuToggle.contains(e.target) && !adminNav.contains(e.target)) {
                mobileMenuToggle.classList.remove('active');
                adminNav.classList.remove('active');
                document.body.classList.remove('menu-open');
            }
        });

        // Cerrar menú al cambiar el tamaño de la ventana
        window.addEventListener('resize', function() {
            if (window.innerWidth > 480) {
                mobileMenuToggle.classList.remove('active');
                adminNav.classList.remove('active');
                document.body.classList.remove('menu-open');
            }
        });
    }
}

// View request details
window.viewRequest = function(requestId) {
    const request = currentRequests.find(req => req.id === requestId);
    if (!request) return;
    
    const modalBody = document.getElementById('view-modal-body');
    modalBody.innerHTML = `
        <div class="request-details">
            <div class="detail-row">
                <strong>ID:</strong> ${request.id}
            </div>
            <div class="detail-row">
                <strong>Nombre:</strong> ${request.nombre}
            </div>
            <div class="detail-row">
                <strong>Email:</strong> ${request.email}
            </div>
            <div class="detail-row">
                <strong>Teléfono:</strong> ${request.telefono}
            </div>
            <div class="detail-row">
                <strong>Mensaje:</strong>
                <p>${request.mensaje}</p>
            </div>
            <div class="detail-row">
                <strong>Fecha:</strong> ${formatDate(request.timestamp)}
            </div>
            <div class="detail-row">
                <strong>Estado:</strong> <span class="status-badge status-${request.status}">${getStatusText(request.status)}</span>
            </div>
            <div class="detail-row">
                <strong>Prioridad:</strong> <span class="priority-badge priority-${request.priority}">${getPriorityText(request.priority)}</span>
            </div>
        </div>
    `;
    
    document.getElementById('view-modal').style.display = 'flex';
}

// Delete request
window.deleteRequest = function(requestId) {
    currentDeleteId = requestId;
    document.getElementById('delete-modal').style.display = 'flex';
}

// Confirm delete
window.confirmDelete = async function() {
    if (!currentDeleteId) return;
    
    try {
        await deleteDoc(doc(db, "contactos", currentDeleteId));
        closeModal('delete-modal');
        showSuccess("Solicitud eliminada correctamente");
    } catch (error) {
        console.error("Error deleting request:", error);
        showError("Error al eliminar la solicitud");
    }
}

// Update status
window.updateStatus = async function(requestId, newStatus) {
    try {
        await updateDoc(doc(db, "contactos", requestId), {
            status: newStatus,
            updatedAt: serverTimestamp()
        });
        showSuccess("Estado actualizado correctamente");
    } catch (error) {
        console.error("Error updating status:", error);
        showError("Error al actualizar el estado");
    }
}

// Update priority
window.updatePriority = async function(requestId, newPriority) {
    try {
        await updateDoc(doc(db, "contactos", requestId), {
            priority: newPriority,
            updatedAt: serverTimestamp()
        });
        showSuccess("Prioridad actualizada correctamente");
    } catch (error) {
        console.error("Error updating priority:", error);
        showError("Error al actualizar la prioridad");
    }
}

// Close modal
window.closeModal = function(modalId) {
    document.getElementById(modalId).style.display = 'none';
    if (modalId === 'delete-modal') {
        currentDeleteId = null;
    }
}

// Utility functions
function getStatusText(status) {
    const statusMap = {
        'pending': 'Pendiente',
        'in_progress': 'En Progreso',
        'completed': 'Completado',
        'cancelled': 'Cancelado'
    };
    return statusMap[status] || status;
}

function getPriorityText(priority) {
    const priorityMap = {
        'low': 'Baja',
        'medium': 'Media',
        'high': 'Alta',
        'urgent': 'Urgente'
    };
    return priorityMap[priority] || priority;
}

function formatDate(date) {
    if (!date) return '';
    return new Date(date).toLocaleString('es-ES', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function showSuccess(message) {
    // Crear notificación de éxito
    const notification = document.createElement('div');
    notification.className = 'success-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Agregar estilos
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        z-index: 10000;
        transform: translateX(400px);
        transition: all 0.5s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        font-weight: 500;
        max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    // Animar entrada
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remover después de 3 segundos
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);
    
    console.log("Success:", message);
}

function showError(message) {
    // Crear notificación de error
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Agregar estilos
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 8px 25px rgba(244, 67, 54, 0.3);
        z-index: 10000;
        transform: translateX(400px);
        transition: all 0.5s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        font-weight: 500;
        max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    // Animar entrada
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remover después de 4 segundos
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 4000);
    
    console.error("Error:", message);
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
}); 