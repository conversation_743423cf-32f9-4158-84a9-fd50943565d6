/* Animaciones */
@keyframes fadeInDown {
    0% { opacity: 0; transform: translateY(-40px); }
    100% { opacity: 1; transform: translateY(0); }
}
@keyframes fadeInUp {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
}
@keyframes slideInLeft {
    0% { opacity: 0; transform: translateX(-50px); }
    100% { opacity: 1; transform: translateX(0); }
}
@keyframes slideInRight {
    0% { opacity: 0; transform: translateX(50px); }
    100% { opacity: 1; transform: translateX(0); }
}
@keyframes mapZoom {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Aplicar animaciones a títulos y overlays */
.hero h1, .overlay h1, .servicios-title, .contacto-title, .about-content h2 {
    animation: fadeInDown 1s ease;
}
.overlay, .about-content, .servicio-card {
    animation: fadeInUp 1.2s ease;
}
.contacto-info {
    animation: slideInLeft 1s ease;
}
.contacto-form {
    animation: slideInRight 1s ease;
}

body {
    margin: 0;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #E91E63 0%, #F06292 25%, #FF9800 75%, #FF7043 100%);
    color: #333333;
    font-weight: 400;
    line-height: 1.6;
    min-height: 100vh;
}

header {
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    padding: 0;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(233, 30, 99, 0.2);
    backdrop-filter: blur(10px);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.2rem 2rem;
}

.logo {
    font-size: 2rem;
    font-weight: 600;
    color: #E91E63;
    letter-spacing: -0.5px;
    font-family: 'Poppins', sans-serif;
}

/* Menú de escritorio */
.desktop-nav {
    display: block;
}

.desktop-nav ul {
    list-style: none;
    display: flex;
    gap: 2.5rem;
    margin: 0;
    padding: 0;
}

.desktop-nav a {
    color: #333333;
    text-decoration: none;
    font-size: 1.15rem;
    font-weight: 500;
    padding-bottom: 0.5rem;
    transition: color 0.2s;
    letter-spacing: -0.2px;
}

.desktop-nav a.active, .desktop-nav a:hover {
    border-bottom: 3px solid #E91E63;
    color: #E91E63;
}

/* Sección del logo y botón admin */
.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Botón de acceso administrativo */
.admin-login {
    margin-left: 0;
}

.admin-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background: rgba(233, 30, 99, 0.1);
    border: 2px solid rgba(233, 30, 99, 0.3);
    border-radius: 50%;
    color: #E91E63;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.admin-btn:hover {
    background: rgba(233, 30, 99, 0.2);
    border-color: #E91E63;
    transform: scale(1.1);
}

/* Menú móvil */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #f8ecc1;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.mobile-nav {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(24, 24, 24, 0.98);
    z-index: 1000;
    padding-top: 80px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.mobile-nav.active {
    transform: translateX(0);
}

.mobile-nav ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    margin: 0;
    padding: 2rem;
}

.mobile-nav a {
    color: #fff;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 500;
    padding: 1rem 2rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: block;
    text-align: center;
    letter-spacing: 0.5px;
}

.mobile-nav a.active, .mobile-nav a:hover {
    background: rgba(248, 236, 193, 0.1);
    color: #f8ecc1;
    transform: translateY(-2px);
}

.mobile-nav a.admin-mobile {
    background: rgba(248, 236, 193, 0.15);
    color: #f8ecc1;
    border: 1px solid rgba(248, 236, 193, 0.3);
    font-size: 1.2rem;
    font-weight: 600;
}

.mobile-nav a.admin-mobile:hover {
    background: rgba(248, 236, 193, 0.25);
    border-color: #f8ecc1;
}

main {
    padding-top: 80px;
}

main.inicio-main {
    padding-top: 0;
}

.hero {
    min-height: 90vh;
    width: 100vw;
    background: url('img/SobreUs.png') no-repeat center center/cover;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
}

.hero-inicio {
    min-height: 100vh;
    background: url('img/SobreUs.png') no-repeat center center/cover;
}

.overlay {
    background: rgba(24, 24, 24, 0.75);
    padding: 3rem 3.5rem 2.5rem 3.5rem;
    margin-left: 5vw;
    border-radius: 8px;
    max-width: 420px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.25);
}

.overlay h1 {
    font-size: 2.8rem;
    font-weight: 600;
    margin: 0 0 1.2rem 0;
    line-height: 1.1;
    letter-spacing: 0.5px;
    font-family: 'Poppins', sans-serif;
}

.line {
    width: 60px;
    height: 5px;
    background: #f8ecc1;
    margin-bottom: 1.2rem;
    border-radius: 2px;
}

.overlay p {
    font-size: 1.2rem;
    margin: 0;
    color: #f8ecc1;
    font-weight: 300;
    letter-spacing: 0.3px;
}

/* Sección Sobre Nosotros (about-section) */
.about-section {
    width: 100vw;
    background: #181818;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 0 4rem 0;
}
.about-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    max-width: 1100px;
    width: 100%;
    margin: 0 2vw;
}
.about-img img {
    width: 340px;
    max-width: 90vw;
    border-radius: 6px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.25);
}
.about-content {
    background: rgba(24, 24, 24, 0.92);
    padding: 2.5rem 2.5rem 2rem 2.5rem;
    border-radius: 8px;
    max-width: 500px;
    color: #fff;
    text-align: center;
    box-shadow: 0 4px 24px rgba(0,0,0,0.25);
}
.about-content h2 {
    font-size: 2.3rem;
    margin: 0 0 1rem 0;
    font-weight: 600;
    letter-spacing: 0.5px;
    font-family: 'Poppins', sans-serif;
}
.about-content .line {
    width: 60px;
    height: 5px;
    background: #f8ecc1;
    margin: 0 auto 1.2rem auto;
    border-radius: 2px;
}
.about-content p {
    font-size: 1.1rem;
    margin: 0;
    color: #fff;
    font-weight: 400;
    letter-spacing: 0.2px;
    line-height: 1.7;
}
/* Media queries para sección Sobre Nosotros */
@media (max-width: 900px) {
    .about-container {
        flex-direction: column;
        gap: 2rem;
        padding: 0 2rem;
    }
    .about-img img {
        width: 85vw;
        max-width: 400px;
    }
    .about-content {
        max-width: 90vw;
    }
}

@media (max-width: 600px) {
    .about-section {
        padding: 0 0 3rem 0;
    }
    .about-container {
        gap: 1.5rem;
        padding: 0 1.5rem;
    }
    .about-img img {
        width: 85vw;
        max-width: 350px;
    }
    .about-content {
        max-width: 90vw;
        padding: 1.5rem;
    }
    .about-content h2 {
        font-size: 1.8rem;
    }
    .about-content p {
        font-size: 1rem;
    }
}

/* Overlay centrado para hero-inicio */
.hero-inicio {
    background: url('img/SobreUs.png') no-repeat center center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100vw;
    position: relative;
}
.overlay-center {
    margin-left: 0;
    text-align: center;
    align-items: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
}
.overlay-center h1 {
    font-size: 2.8rem;
}
.overlay-center p {
    color: #f8ecc1;
    font-size: 1.2rem;
}

/* Media Queries para Responsividad Completa */

/* Tablets y dispositivos medianos */
@media (max-width: 1024px) {
    .container {
        padding: 1rem 1.5rem;
    }
    .logo {
        font-size: 1.8rem;
    }
    nav a {
        font-size: 1rem;
    }
    .overlay {
        padding: 2.5rem;
        max-width: 85vw;
    }
    .overlay h1 {
        font-size: 2.5rem;
    }
    .about-container {
        gap: 2rem;
        margin: 0 1.5vw;
    }
    .servicios-cards {
        gap: 1.5rem;
    }
}

/* Tablets pequeñas y móviles grandes */
@media (max-width: 768px) {
    .container {
        padding: 0.8rem 1rem;
    }
    .logo {
        font-size: 1.6rem;
    }
    .desktop-nav {
        display: none;
    }
    .mobile-menu-toggle {
        display: flex;
    }
    
    .admin-login {
        display: none;
    }
    
    .logo-section {
        flex-direction: column;
        gap: 0.5rem;
    }
    .mobile-nav {
        display: block;
    }
    .overlay {
        padding: 2rem;
        max-width: 90vw;
        margin-left: 0;
    }
    .overlay h1 {
        font-size: 2.2rem;
    }
    .overlay p {
        font-size: 1.1rem;
    }
    .about-container {
        flex-direction: column;
        gap: 1.5rem;
    }
    .about-img img {
        width: 85vw;
        max-width: 350px;
    }
    .about-content {
        max-width: 90vw;
        padding: 2rem;
    }
    .about-content h2 {
        font-size: 2rem;
    }
    .servicios-cards {
        flex-direction: column;
        align-items: center;
    }
    .servicio-card {
        width: 90vw;
        max-width: 400px;
    }
    .contacto-container {
        flex-direction: column;
        gap: 2rem;
    }
    .contacto-form, .contacto-info {
        width: 90vw;
        max-width: 450px;
    }
    .form-row {
        flex-direction: column;
        gap: 0.5rem;
    }
    .footer-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    .footer-content nav ul {
        gap: 1.5rem;
    }
}

/* Móviles medianos */
@media (max-width: 480px) {
    .container {
        flex-direction: column;
        gap: 0.8rem;
        padding: 0.8rem;
    }
    .logo {
        font-size: 1.5rem;
    }
    nav ul {
        gap: 1rem;
    }
    nav a {
        font-size: 0.9rem;
        padding-bottom: 0.3rem;
    }
    main {
        padding-top: 90px;
    }
    .overlay {
        padding: 1.5rem;
        max-width: 92vw;
        margin: 0 1rem;
    }
    .overlay h1 {
        font-size: 1.8rem;
        line-height: 1.2;
    }
    .overlay p {
        font-size: 1rem;
    }
    .line {
        width: 50px;
        height: 4px;
    }
    .about-content {
        padding: 1.5rem;
    }
    .about-content h2 {
        font-size: 1.8rem;
    }
    .about-content p {
        font-size: 1rem;
    }
    .servicios-title, .contacto-title {
        font-size: 2rem;
    }
    .servicios-cards {
        padding: 0 1rem;
    }
    .servicio-card {
        width: 80vw;
        max-width: 300px;
    }
    .contacto-form input, .contacto-form textarea {
        padding: 0.8rem 0.8rem 0.8rem 2.5rem;
        font-size: 0.9rem;
    }
    .contacto-form button {
        padding: 0.9rem;
        font-size: 0.95rem;
    }
    .info-item {
        padding: 1rem;
    }
    .info-item p {
        font-size: 0.9rem;
    }
    .footer-content {
        padding: 0 1rem;
    }
    .footer-content > div {
        font-size: 0.85rem;
    }
    .footer-content nav a {
        font-size: 0.8rem;
    }
}

/* Móviles pequeños */
@media (max-width: 360px) {
    .container {
        padding: 0.6rem;
    }
    .logo {
        font-size: 1.3rem;
    }
    nav ul {
        gap: 0.8rem;
    }
    nav a {
        font-size: 0.85rem;
    }
    .overlay {
        padding: 1.2rem;
        max-width: 90vw;
        margin: 0 1rem;
    }
    .overlay h1 {
        font-size: 1.6rem;
    }
    .overlay p {
        font-size: 0.9rem;
    }
    .about-content h2 {
        font-size: 1.6rem;
    }
    .servicios-title, .contacto-title {
        font-size: 1.8rem;
    }
    .servicios-cards {
        padding: 0 0.8rem;
    }
    .servicio-card {
        width: 75vw;
        max-width: 280px;
    }
    .contacto-form input, .contacto-form textarea {
        padding: 0.7rem 0.7rem 0.7rem 2.2rem;
        font-size: 0.85rem;
    }
    .input-group i {
        font-size: 0.8rem;
        left: 0.8rem;
    }
}

/* Orientación landscape en móviles */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }
    .overlay {
        padding: 1.5rem;
        max-width: 80vw;
    }
    .overlay h1 {
        font-size: 2rem;
        margin-bottom: 0.8rem;
    }
    .overlay p {
        font-size: 1rem;
    }
    .line {
        margin-bottom: 0.8rem;
    }
    main {
        padding-top: 70px;
    }
}

/* Dispositivos de alta resolución */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }
    .overlay {
        max-width: 500px;
    }
    .about-container {
        max-width: 1300px;
    }
    .servicios-cards {
        gap: 2.5rem;
    }
    .contacto-container {
        max-width: 1200px;
    }
} 

/* Sección de Servicios */
.servicios-section {
    width: 100vw;
    min-height: 90vh;
    background: #181818;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4rem 0 4rem 0;
}
.servicios-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.5rem;
    text-align: center;
    letter-spacing: 0.5px;
    font-family: 'Poppins', sans-serif;
}
.servicios-section .line {
    width: 60px;
    height: 5px;
    background: #f8ecc1;
    margin: 0 auto 2.5rem auto;
    border-radius: 2px;
}
.servicios-cards {
    display: flex;
    flex-direction: row;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
    max-width: 1200px;
}
.servicio-card {
    background: #111;
    border: 2px solid #f8ecc1;
    border-radius: 10px;
    width: 280px;
    min-height: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.8rem 1rem 1.2rem 1rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.18);
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 1.5rem;
}
.servicio-card:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 10px 32px rgba(0,0,0,0.28);
}
.servicio-img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 2px solid #fff;
    background: #222;
    display: flex;
    align-items: center;
    justify-content: center;
}
.servicio-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}
.servicio-card h3 {
    color: #fff;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0.4rem 0 0.6rem 0;
    text-align: center;
    letter-spacing: 0.3px;
    font-family: 'Poppins', sans-serif;
}
.servicio-card p {
    color: #fff;
    font-size: 1rem;
    text-align: center;
    margin: 0;
    font-weight: 400;
    letter-spacing: 0.2px;
    line-height: 1.5;
}
/* Media queries para sección de Servicios */
@media (max-width: 1100px) {
    .servicios-cards {
        flex-direction: column;
        align-items: center;
        padding: 0 2rem;
    }
    .servicio-card {
        width: 85vw;
        max-width: 400px;
    }
}

@media (max-width: 600px) {
    .servicios-section {
        padding: 3rem 0 3rem 0;
    }
    .servicios-title {
        font-size: 2.2rem;
    }
    .servicios-cards {
        padding: 0 1.5rem;
    }
    .servicio-card {
        width: 85vw;
        max-width: 320px;
        padding: 1.8rem 1.2rem 1.2rem 1.2rem;
    }
    .servicio-img {
        width: 140px;
        height: 140px;
    }
    .servicio-card h3 {
        font-size: 1.2rem;
    }
    .servicio-card p {
        font-size: 1rem;
    }
} 

/* Formulario de contacto */
.contacto-section {
    width: 100vw;
    min-height: 60vh;
    background: #181818;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4rem 0 2rem 0;
}
.contacto-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.5rem;
    text-align: center;
    letter-spacing: 0.5px;
    font-family: 'Poppins', sans-serif;
}
.contacto-section .line {
    width: 60px;
    height: 5px;
    background: #f8ecc1;
    margin: 0 auto 2.5rem auto;
    border-radius: 2px;
}
.contacto-container {
    display: flex;
    flex-direction: row;
    gap: 3rem;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    max-width: 1100px;
    margin: 0 2vw;
}
.contacto-info {
    flex: 1;
    background: rgba(24, 24, 24, 0.92);
    padding: 2.5rem;
    border-radius: 8px;
    color: #fff;
    box-shadow: 0 4px 24px rgba(0,0,0,0.25);
    max-width: 400px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}
.info-item {
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}
.info-item:hover {
    transform: translateX(10px);
}
.info-item i {
    font-size: 1.2rem;
    color: #f8ecc1;
    margin-right: 1rem;
    width: 20px;
    text-align: center;
}
.info-item p {
    margin: 0;
    font-size: 1.1rem;
    color: #fff;
    font-weight: 400;
    letter-spacing: 0.2px;
}
.contacto-form {
    flex: 1.2;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    background: rgba(24,24,24,0.92);
    padding: 2.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.25);
    min-width: 320px;
    max-width: 480px;
}
.form-row {
    display: flex;
    gap: 1rem;
}
.input-group {
    position: relative;
    width: 100%;
}
.input-group i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #f8ecc1;
    font-size: 1rem;
    z-index: 2;
}
.input-group textarea + i {
    top: 1.5rem;
    transform: none;
}
.contacto-form input, .contacto-form textarea {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border-radius: 5px;
    border: 1px solid #f8ecc1;
    background: #222;
    color: #fff;
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    transition: all 0.3s ease;
    box-sizing: border-box;
}
.contacto-form textarea {
    padding-left: 3rem;
    padding-top: 1rem;
    min-height: 120px;
    resize: vertical;
}
.contacto-form input:focus, .contacto-form textarea:focus {
    outline: none;
    border: 2px solid #f8ecc1;
    box-shadow: 0 0 10px rgba(248, 236, 193, 0.3);
    transform: translateY(-2px);
}
.contacto-form button {
    padding: 1.2rem;
    background: linear-gradient(135deg, #f8ecc1 0%, #e2c98d 100%);
    color: #181818;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    font-family: 'Poppins', sans-serif;
}
.contacto-form button:hover {
    background: linear-gradient(135deg, #e2c98d 0%, #f8ecc1 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(248, 236, 193, 0.4);
}
.contacto-form button i {
    font-size: 1rem;
}
/* Media queries específicas para contacto */
@media (max-width: 900px) {
    .contacto-container {
        flex-direction: column;
        gap: 2rem;
        align-items: center;
        padding: 0 2rem;
    }
    .contacto-form, .contacto-info {
        width: 90vw;
        max-width: 480px;
    }
    .form-row {
        flex-direction: column;
        gap: 0.5rem;
    }
    .input-group i {
        font-size: 0.9rem;
    }
    .contacto-form input, .contacto-form textarea {
        padding: 0.9rem 0.9rem 0.9rem 2.5rem;
        font-size: 0.95rem;
    }
    .contacto-form button {
        padding: 1rem;
        font-size: 1rem;
    }
}

@media (max-width: 600px) {
    .contacto-container {
        padding: 0 1.5rem;
    }
    .contacto-form, .contacto-info {
        width: 90vw;
        max-width: 400px;
        padding: 2rem;
    }
    .contacto-form input, .contacto-form textarea {
        padding: 0.8rem 0.8rem 0.8rem 2.5rem;
        font-size: 0.9rem;
    }
    .contacto-form button {
        padding: 0.9rem;
        font-size: 0.95rem;
    }
    .info-item {
        padding: 0.8rem;
    }
    .info-item p {
        font-size: 0.9rem;
    }
}

/* Mapa y footer mejorado */
.mapa-section {
    width: 100vw;
    background: #181818;
    padding: 0;
    display: flex;
    justify-content: center;
    height: calc(100vh - 160px); /* Altura completa menos header y footer */
    margin: 0;
}
.mapa-container {
    width: 100%;
    height: 100%;
    animation: mapZoom 1.5s ease;
    position: relative;
    overflow: hidden;
}
.mapa-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(248, 236, 193, 0.1) 0%, transparent 50%, rgba(248, 236, 193, 0.1) 100%);
    animation: pulse 3s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}
.mapa-section iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 0;
    box-shadow: none;
    position: relative;
    z-index: 2;
}

/* Footer mejorado para todas las páginas */
footer {
    width: 100vw;
    background: #111;
    padding: 2.5rem 0 2rem 0;
    margin-top: 0;
    border-top: 1px solid #333;
}
.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
    gap: 2rem;
}
.footer-content > div {
    color: #f8ecc1;
    font-size: 0.95rem;
    line-height: 1.6;
    font-weight: 400;
    letter-spacing: 0.2px;
}
.footer-content nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
    margin: 0;
    padding: 0;
}
.footer-content nav a {
    color: #f8ecc1;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.2s;
    letter-spacing: 0.8px;
    font-family: 'Poppins', sans-serif;
}
.footer-content nav a:hover {
    color: #fff;
}
/* Media queries para footer y mapa */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
        padding: 0 1rem;
    }
    .footer-content nav ul {
        gap: 1.5rem;
    }
    .footer-content > div {
        font-size: 0.9rem;
    }
    .footer-content nav a {
        font-size: 0.85rem;
    }
    .mapa-section {
        height: calc(100vh - 200px);
    }
    .mapa-section iframe {
        width: 100%;
        height: 100%;
    }
}

@media (max-width: 480px) {
    .footer-content {
        padding: 0 0.8rem;
    }
    .footer-content > div {
        font-size: 0.85rem;
    }
    .footer-content nav a {
        font-size: 0.8rem;
    }
    .mapa-section {
        height: calc(100vh - 180px);
    }
}

/* Mensaje de confirmación de contacto */
.success-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.success-content {
    background: #181818;
    border: 2px solid #f8ecc1;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    max-width: 400px;
    margin: 20px;
    animation: slideInUp 0.5s ease;
}

.success-content i {
    font-size: 3rem;
    color: #4CAF50;
    margin-bottom: 1rem;
}

.success-content h3 {
    color: #f8ecc1;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.success-content p {
    color: #ccc;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.close-btn {
    background: linear-gradient(135deg, #f8ecc1 0%, #e6d4a3 100%);
    color: #181818;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.close-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(248, 236, 193, 0.3);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
} 