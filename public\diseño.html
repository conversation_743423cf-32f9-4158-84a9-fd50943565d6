<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Desing - Diseños</title>
    <link rel="stylesheet" href="inicio.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Estilos específicos para la página de diseños */
        .diseños-section {
            width: 100%;
            min-height: 90vh;
            background: transparent;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 4rem 2rem 4rem 2rem;
        }

        .diseños-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.5rem;
            text-align: center;
            letter-spacing: 0.5px;
            font-family: 'Poppins', sans-serif;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .diseños-section .line {
            width: 60px;
            height: 5px;
            background: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0.8));
            margin: 0 auto 2.5rem auto;
            border-radius: 2px;
        }

        .diseños-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            width: 100%;
            padding: 0 2rem;
            justify-items: center;
        }

        .diseño-card {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            box-shadow: 0 8px 32px rgba(233, 30, 99, 0.2);
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .diseño-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(233, 30, 99, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .diseño-card:hover::before {
            left: 100%;
        }

        .diseño-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(233, 30, 99, 0.3);
            border-color: #E91E63;
        }

        .diseño-img {
            width: 200px;
            height: 200px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 1.5rem;
            border: 3px solid #E91E63;
            background: linear-gradient(135deg, #E91E63, #F06292);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .diseño-img:hover {
            transform: rotate(5deg) scale(1.05);
            box-shadow: 0 10px 25px rgba(233, 30, 99, 0.4);
        }

        .diseño-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        }

        .diseño-img i {
            font-size: 4rem;
            color: #ffffff;
            opacity: 0.8;
        }

        .diseño-card h3 {
            color: #333333;
            font-size: 1.4rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
            text-align: center;
            letter-spacing: 0.3px;
            font-family: 'Poppins', sans-serif;
        }

        .precio-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem 0;
            position: relative;
        }

        .precio {
            font-size: 2rem;
            font-weight: 700;
            color: #E91E63;
            text-shadow: 2px 2px 4px rgba(233, 30, 99, 0.3);
            animation: pulsePrice 2s ease-in-out infinite;
        }

        .moneda {
            font-size: 1.2rem;
            color: #666666;
            margin-right: 0.3rem;
        }

        @keyframes pulsePrice {
            0%, 100% {
                transform: scale(1);
                text-shadow: 2px 2px 4px rgba(233, 30, 99, 0.3);
            }
            50% {
                transform: scale(1.1);
                text-shadow: 4px 4px 8px rgba(233, 30, 99, 0.5);
            }
        }

        .diseño-card p {
            color: #666666;
            font-size: 1rem;
            text-align: center;
            margin: 0 0 1.5rem 0;
            font-weight: 400;
            letter-spacing: 0.2px;
            line-height: 1.5;
        }

        .btn-comprar {
            background: linear-gradient(135deg, #E91E63 0%, #F06292 100%);
            color: #ffffff;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.4rem;
        }

        .btn-comprar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-comprar:hover::before {
            left: 100%;
        }

        .btn-comprar:hover {
            background: linear-gradient(135deg, #F06292 0%, #FF9800 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
        }

        /* Animación de entrada para las cards */
        .diseño-card {
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .diseño-card:nth-child(1) { animation-delay: 0.1s; }
        .diseño-card:nth-child(2) { animation-delay: 0.3s; }
        .diseño-card:nth-child(3) { animation-delay: 0.5s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .diseños-section {
                padding: 2rem 1.5rem;
            }

            .diseños-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                padding: 0 1.5rem;
                max-width: 100%;
                justify-items: center;
            }

            .diseño-card {
                margin: 0 auto;
                max-width: 350px;
                width: 100%;
            }

            .diseño-img {
                width: 150px;
                height: 150px;
            }

            .diseños-title {
                font-size: 2rem;
            }

            .precio {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .diseños-section {
                padding: 1.5rem 1rem;
            }

            .diseños-grid {
                padding: 0 1rem;
                justify-items: center;
            }

            .diseño-card {
                padding: 1rem;
                margin: 0 auto;
                max-width: 300px;
                width: 100%;
            }

            .diseño-img {
                width: 120px;
                height: 120px;
            }

            .diseños-title {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo-section">
                <div class="logo">Print Desing</div>
                <div class="admin-login">
                    <a href="login.html" class="admin-btn" title="Acceso Administrativo">
                        <i class="fas fa-user-shield"></i>
                    </a>
                </div>
            </div>
            <nav class="desktop-nav">
                <ul>
                    <li><a href="inicio.html">Página De Inicio</a></li>
                    <li><a href="nosotros.html">Sobre Nosotros</a></li>
                    <li><a href="servicio.html">Servicios</a></li>
                    <li><a href="diseño.html" class="active">Diseños</a></li>
                    <li><a href="contacto.html">Contacto</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
        <nav class="mobile-nav">
            <ul>
                <li><a href="inicio.html">Página De Inicio</a></li>
                <li><a href="nosotros.html">Sobre Nosotros</a></li>
                <li><a href="servicio.html">Servicios</a></li>
                <li><a href="diseño.html" class="active">Diseños</a></li>
                <li><a href="contacto.html">Contacto</a></li>
                <li><a href="login.html" class="admin-mobile">Acceso Administrativo</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <section class="diseños-section">
            <h2 class="diseños-title">DISEÑOS</h2>
            <div class="line"></div>
            <div class="diseños-grid">
                <div class="diseño-card">
                    <div class="diseño-img">
                        <img src="img/comp-1.jpeg" alt="Diseño Personalizado #1">
                    </div>
                    <h3>Diseño Personalizado #1</h3>
                    <p>Diseño único y creativo para camisetas, con colores vibrantes y estilo moderno.</p>
                    <div class="precio-container">
                        <span class="moneda">S/</span>
                        <span class="precio">15.00</span>
                    </div>
                    <a href="contacto.html" class="btn-comprar">
                        <i class="fas fa-shopping-cart"></i>
                        Comprar Ahora
                    </a>
                </div>
                
                <div class="diseño-card">
                    <div class="diseño-img">
                        <img src="img/comp-2.jpeg" alt="Diseño Personalizado #2">
                    </div>
                    <h3>Diseño Personalizado #2</h3>
                    <p>Arte gráfico profesional perfecto para cualquier prenda, con acabados de alta calidad.</p>
                    <div class="precio-container">
                        <span class="moneda">S/</span>
                        <span class="precio">15.00</span>
                    </div>
                    <a href="contacto.html" class="btn-comprar">
                        <i class="fas fa-shopping-cart"></i>
                        Comprar Ahora
                    </a>
                </div>
                
                <div class="diseño-card">
                    <div class="diseño-img">
                        <img src="img/comp-3.jpeg" alt="Diseño Personalizado #3">
                    </div>
                    <h3>Diseño Personalizado #3</h3>
                    <p>Diseño exclusivo con técnicas avanzadas de impresión y materiales premium.</p>
                    <div class="precio-container">
                        <span class="moneda">S/</span>
                        <span class="precio">15.00</span>
                    </div>
                    <a href="contacto.html" class="btn-comprar">
                        <i class="fas fa-shopping-cart"></i>
                        Comprar Ahora
                    </a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div>© 2024 Print Desing. Todos los derechos reservados.</div>
            <nav>
                <ul>
                    <li><a href="inicio.html">PÁGINA DE INICIO</a></li>
                    <li><a href="nosotros.html">SOBRE NOSOTROS</a></li>
                    <li><a href="servicio.html">SERVICIOS</a></li>
                    <li><a href="diseño.html">DISEÑOS</a></li>
                    <li><a href="contacto.html">CONTACTO</a></li>
                </ul>
            </nav>
        </div>
    </footer>

    <script src="script.js"></script>
    <script type="module">
  // Import the functions you need from the SDKs you need
  import { initializeApp } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-app.js";
  import { getAnalytics } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-analytics.js";
  // TODO: Add SDKs for Firebase products that you want to use
  // https://firebase.google.com/docs/web/setup#available-libraries

  // Your web app's Firebase configuration
  // For Firebase JS SDK v7.20.0 and later, measurementId is optional
  const firebaseConfig = {
    apiKey: "AIzaSyBvOoOlk2pskzNBdNpjQFgkl_QxmPaCYbE",
    authDomain: "print-desing.firebaseapp.com",
    projectId: "print-desing",
    storageBucket: "print-desing.firebasestorage.app",
    messagingSenderId: "21705479817",
    appId: "1:21705479817:web:d5c5c9c8a4c5c9c8a4c5c9",
    measurementId: "G-XXXXXXXXXX"
  };

  // Initialize Firebase
  const app = initializeApp(firebaseConfig);
  const analytics = getAnalytics(app);
</script>
</body>
</html>
