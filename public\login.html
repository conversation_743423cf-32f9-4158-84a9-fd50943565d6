<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Desing - Acceso Administrativo</title>
    <link rel="stylesheet" href="inicio.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #181818 0%, #2a2a2a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(248, 236, 193, 0.2);
            border-radius: 20px;
            padding: 3rem;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 0.8s ease;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: #f8ecc1;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #ccc;
            font-size: 0.9rem;
            margin: 0;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            position: relative;
        }

        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #f8ecc1;
            font-size: 1.1rem;
        }

        .form-group input {
            width: 100%;
            padding: 15px 15px 15px 50px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(248, 236, 193, 0.3);
            border-radius: 10px;
            color: #fff;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-group input:focus {
            outline: none;
            border-color: #f8ecc1;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(248, 236, 193, 0.2);
        }

        .login-btn {
            background: linear-gradient(135deg, #f8ecc1 0%, #e6d4a3 100%);
            color: #181818;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(248, 236, 193, 0.3);
        }

        .back-link {
            text-align: center;
            margin-top: 2rem;
        }

        .back-link a {
            color: #f8ecc1;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .back-link a:hover {
            color: #e6d4a3;
        }

        .error-message {
            background: rgba(255, 59, 48, 0.1);
            border: 1px solid rgba(255, 59, 48, 0.3);
            color: #ff6b6b;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            display: none;
        }

        /* Responsive para tablets (768px - 1023px) */
        @media (min-width: 768px) and (max-width: 1023px) {
            .login-container {
                max-width: 450px;
                padding: 2.5rem;
            }
            
            .login-header h1 {
                font-size: 2.2rem;
            }
            
            .form-group input {
                padding: 18px 15px 18px 55px;
                font-size: 1.1rem;
            }
            
            .login-btn {
                padding: 18px;
                font-size: 1.2rem;
            }
        }
        
        /* Responsive para laptops (1024px - 1366px) */
        @media (min-width: 1024px) and (max-width: 1366px) {
            .login-container {
                max-width: 500px;
                padding: 3rem;
            }
            
            .login-header h1 {
                font-size: 2.5rem;
            }
            
            .form-group input {
                padding: 20px 15px 20px 60px;
                font-size: 1.2rem;
            }
            
            .login-btn {
                padding: 20px;
                font-size: 1.3rem;
            }
        }
        
        /* Responsive para desktop (1367px+) */
        @media (min-width: 1367px) {
            .login-container {
                max-width: 550px;
                padding: 3.5rem;
            }
            
            .login-header h1 {
                font-size: 2.8rem;
            }
            
            .form-group input {
                padding: 22px 15px 22px 65px;
                font-size: 1.3rem;
            }
            
            .login-btn {
                padding: 22px;
                font-size: 1.4rem;
            }
        }
        
        /* Responsive para móviles pequeños (max-width: 480px) */
        @media (max-width: 480px) {
            .login-container {
                padding: 2rem;
                margin: 10px;
                max-width: 100%;
            }

            .login-header h1 {
                font-size: 1.5rem;
            }
            
            .form-group input {
                padding: 12px 15px 12px 45px;
                font-size: 0.95rem;
            }
            
            .login-btn {
                padding: 12px;
                font-size: 1rem;
            }
        }
        
        /* Responsive para móviles medianos (481px - 767px) */
        @media (min-width: 481px) and (max-width: 767px) {
            .login-container {
                padding: 2.5rem;
                max-width: 400px;
            }
            
            .login-header h1 {
                font-size: 1.8rem;
            }
            
            .form-group input {
                padding: 15px 15px 15px 50px;
                font-size: 1rem;
            }
            
            .login-btn {
                padding: 15px;
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Acceso Administrativo</h1>
            <p>Print Desing - Panel de Control</p>
        </div>
        
        <div class="error-message" id="error-message">
            Credenciales incorrectas. Por favor, inténtalo de nuevo.
        </div>
        
        <form class="login-form" id="login-form">
            <div class="form-group">
                <i class="fas fa-user"></i>
                <input type="email" id="username" placeholder="Email" required>
            </div>
            
            <div class="form-group">
                <i class="fas fa-lock"></i>
                <input type="password" id="password" placeholder="Contraseña" required>
            </div>
            
            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                Iniciar Sesión
            </button>
        </form>
        
        <div class="back-link">
            <a href="inicio.html">
                <i class="fas fa-arrow-left"></i>
                Volver al sitio
            </a>
        </div>
    </div>

    <script type="module" src="firebase-auth.js"></script>
</body>
</html>