<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Desing - Panel de Administración</title>
    <link rel="stylesheet" href="admin-panel.css?v=2.0">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header class="admin-header">
        <div class="header-container">
            <h1><i class="fas fa-shield-alt"></i> Panel de Administración</h1>
            <nav class="admin-nav">
                <a href="#" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="inicio.html"><i class="fas fa-home"></i> Volver al Sitio</a>
                <a href="login.html"><i class="fas fa-sign-out-alt"></i> Cerrar Sesión</a>
            </nav>
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <main class="admin-container">
        <!-- Estadísticas del Dashboard -->
        <section class="dashboard-stats">
            <div class="stat-card">
                <i class="fas fa-envelope"></i>
                <h3 id="pending-messages">0</h3>
                <p>Mensajes Pendientes</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-clock"></i>
                <h3 id="this-week">0</h3>
                <p>Esta Semana</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-check-circle"></i>
                <h3 id="completed-month">0</h3>
                <p>Completados este Mes</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-exclamation-triangle"></i>
                <h3 id="urgent">0</h3>
                <p>Urgentes</p>
            </div>
        </section>

        <!-- Sección de Solicitudes de Contacto -->
        <section class="requests-section">
            <div class="section-header">
                <h2><i class="fas fa-envelope"></i> Solicitudes de Contacto</h2>
                <div class="filter-controls">
                    <select id="status-filter">
                        <option value="">Todos los estados</option>
                        <option value="pending">Pendiente</option>
                        <option value="in_progress">En Progreso</option>
                        <option value="completed">Completado</option>
                        <option value="cancelled">Cancelado</option>
                    </select>
                    <select id="priority-filter">
                        <option value="">Todas las prioridades</option>
                        <option value="low">Baja</option>
                        <option value="medium">Media</option>
                        <option value="high">Alta</option>
                        <option value="urgent">Urgente</option>
                    </select>
                    <input type="text" id="search-input" placeholder="Buscar por nombre o email...">
                    <button id="clear-filters" class="btn-clear-filters">
                        <i class="fas fa-times"></i> Limpiar Filtros
                    </button>
                </div>
            </div>

            <div class="table-container">
                <!-- Indicador de carga -->
                <div id="loading-indicator" class="loading-indicator">
                    <div class="loading-spinner"></div>
                    <p>Cargando datos...</p>
                </div>

                <table class="requests-table" id="requests-table" style="display: none;">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nombre</th>
                            <th>Email</th>
                            <th>Teléfono</th>
                            <th>Mensaje</th>
                            <th>Estado</th>
                            <th>Prioridad</th>
                            <th>Fecha</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="requests-tbody">
                        <!-- Los datos se cargarán dinámicamente -->
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <!-- Modal para ver solicitud -->
    <div id="view-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-eye"></i> Detalles de la Solicitud</h3>
                <button class="close-modal" onclick="closeModal('view-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="view-modal-body">
                <!-- Contenido dinámico -->
            </div>
            <div class="modal-footer">
                <button class="btn-modal btn-modal-secondary" onclick="closeModal('view-modal')">
                    <i class="fas fa-times"></i> Cerrar
                </button>
            </div>
        </div>
    </div>

    <!-- Modal para eliminar solicitud -->
    <div id="delete-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Confirmar Eliminación</h3>
                <button class="close-modal" onclick="closeModal('delete-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ff6b6b; margin-bottom: 1rem; opacity: 0.8;"></i>
                    <h4 style="color: #f8ecc1; margin-bottom: 1rem; font-size: 1.3rem;">Confirmar Eliminación</h4>
                    <p style="color: #ccc; margin-bottom: 0.5rem;">¿Estás seguro de que quieres eliminar esta solicitud?</p>
                    <p style="color: #ff6b6b; font-weight: 600; font-size: 0.9rem;">Esta acción no se puede deshacer.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-modal btn-modal-secondary" onclick="closeModal('delete-modal')">
                    <i class="fas fa-times"></i> Cancelar
                </button>
                <button class="btn-modal btn-modal-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i> Eliminar
                </button>
            </div>
        </div>
    </div>

    <script type="module" src="firebase-admin.js"></script>
    

</body>
</html> 