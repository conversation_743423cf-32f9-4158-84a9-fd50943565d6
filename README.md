# Print Design - Proyecto Web con Firebase

## 🚀 Proyecto Desplegado
Tu sitio web está disponible en: **https://print-design-pd.web.app**

## 📋 Configuración Completa de Firebase

### ✅ Lo que ya está configurado:

1. **Firebase Hosting**: El sitio está desplegado y funcionando
2. **Firebase Firestore**: Base de datos configurada para almacenar formularios de contacto
3. **Firebase Authentication**: Sistema de login para el panel de administración
4. **Formulario de Contacto**: Conectado con Firestore
5. **Panel de Administración**: Conectado con Firebase para ver y gestionar contactos

### 🔧 Archivos de Firebase creados:

- `firebase-config.js`: Configuración principal de Firebase
- `firebase-contact.js`: Manejo del formulario de contacto
- `firebase-auth.js`: Sistema de autenticación
- `firebase-admin.js`: Panel de administración

### 📊 Estructura de la Base de Datos:

**Colección: `contactos`**
- `nombre` (string): Nombre del cliente
- `email` (string): Email del cliente
- `telefono` (string): Teléfono del cliente
- `mensaje` (string): Mensaje del cliente
- `timestamp` (timestamp): Fecha y hora automática
- `status` (string): Estado (pending, in_progress, completed, cancelled)
- `priority` (string): Prioridad (low, medium, high, urgent)

## 📊 Cómo ver la base de datos

### Opción 1: Firebase Console (Recomendado)
1. Ve a: https://console.firebase.google.com/project/print-design-pd
2. Inicia sesión con tu cuenta de Google
3. En el menú lateral, haz clic en **"Firestore Database"**
4. Verás la colección **"contactos"** con todos los mensajes enviados

### Opción 2: Panel de Administración
1. Ve a tu sitio: https://print-design-pd.web.app
2. Haz clic en el ícono de administrador (escudo) en la esquina superior derecha
3. Inicia sesión con las credenciales de administrador
4. Verás todos los mensajes de contacto en tiempo real

## 🔐 Configuración de Autenticación

### Para crear un usuario administrador:

1. Ve a Firebase Console: https://console.firebase.google.com/project/print-design-pd
2. En el menú lateral, haz clic en **"Authentication"**
3. Ve a la pestaña **"Users"**
4. Haz clic en **"Add User"**
5. Ingresa:
   - **Email**: <EMAIL>
   - **Password**: Tenko11037
6. Haz clic en **"Add User"**

### Credenciales de acceso:
- **Email**: <EMAIL>
- **Contraseña**: Tenko11037

## 📁 Estructura del Proyecto

```
public/
├── inicio.html          # Página principal
├── contacto.html        # Formulario de contacto
├── login.html          # Página de login
├── admin-panel.html    # Panel de administración
├── nosotros.html       # Página sobre nosotros
├── servicio.html       # Página de servicios
├── firebase-config.js  # Configuración de Firebase
├── firebase-contact.js # Manejo del formulario
├── firebase-auth.js    # Autenticación
├── firebase-admin.js   # Panel de administración
├── inicio.css          # Estilos principales
├── admin-panel.css     # Estilos del panel
├── script.js           # Scripts generales
└── img/               # Imágenes del sitio
```

## 🔄 Flujo de Datos

1. **Usuario envía formulario** → Se guarda en Firestore
2. **Administrador inicia sesión** → Ve los mensajes en tiempo real
3. **Administrador gestiona** → Puede ver detalles y eliminar mensajes

## 🛠️ Comandos Útiles

### Desplegar cambios:
```bash
firebase deploy
```

### Servir localmente:
```bash
firebase serve
```

### Ver logs:
```bash
firebase functions:log
```

## 📱 Funcionalidades Implementadas

### ✅ Formulario de Contacto
- Guarda datos en Firestore
- Muestra mensaje de confirmación
- Validación de campos

### ✅ Panel de Administración
- Autenticación segura
- Vista en tiempo real de mensajes
- Filtros por estado y prioridad
- Búsqueda por nombre o email
- Eliminación de mensajes

### ✅ Estadísticas en Tiempo Real
- Mensajes pendientes
- Mensajes de esta semana
- Mensajes completados
- Mensajes urgentes

## 🔒 Seguridad

- Autenticación requerida para acceder al panel
- Datos protegidos en Firestore
- Reglas de seguridad configuradas

## 📞 Soporte

Si necesitas ayuda:
1. Revisa la consola del navegador para errores
2. Verifica la configuración en Firebase Console
3. Asegúrate de que las reglas de Firestore permitan lectura/escritura

## 🚀 Próximos Pasos

1. **Personalizar credenciales**: Cambia el email y contraseña del administrador
2. **Configurar notificaciones**: Agregar notificaciones por email
3. **Analytics**: Configurar Google Analytics
4. **Backup**: Configurar backup automático de datos

---

**¡Tu sitio web está completamente funcional con Firebase!** 🎉 