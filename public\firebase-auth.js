// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-app.js";
import { getAuth, signInWithEmailAndPassword, onAuthStateChanged, signOut } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-auth.js";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyA0tjUO8tJKGgDJFTWpoMKgQQqsdHYDeBE",
  authDomain: "print-design-pd.firebaseapp.com",
  projectId: "print-design-pd",
  storageBucket: "print-design-pd.firebasestorage.app",
  messagingSenderId: "678212447286",
  appId: "1:678212447286:web:81c6c1f16ebd599d68bc97",
  measurementId: "G-E98ZKDHSKK"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Check authentication state
onAuthStateChanged(auth, (user) => {
    if (user) {
        // User is signed in
        console.log("User is signed in:", user.email);
        
        // If we're on the login page, redirect to admin panel
        if (window.location.pathname.includes('login.html')) {
            window.location.href = 'admin-panel.html';
        }
        
        // If we're on admin panel, show content
        if (window.location.pathname.includes('admin-panel.html')) {
            showAdminContent();
        }
    } else {
        // User is signed out
        console.log("User is signed out");
        
        // If we're on admin panel, redirect to login
        if (window.location.pathname.includes('admin-panel.html')) {
            window.location.href = 'login.html';
        }
    }
});

// Handle login form submission
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('error-message');
            const submitBtn = this.querySelector('button[type="submit"]');
            
            // Show loading state
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Iniciando...';
            submitBtn.disabled = true;
            errorMessage.style.display = 'none';
            
            try {
                // Sign in with Firebase
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                console.log("User signed in:", userCredential.user.email);
                
                // Redirect to admin panel
                window.location.href = 'admin-panel.html';
                
            } catch (error) {
                console.error("Login error:", error);
                errorMessage.style.display = 'block';
                errorMessage.textContent = 'Credenciales incorrectas. Usa: <EMAIL> / Tenko11037';
            } finally {
                // Restore button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });
    }
    
    // Handle logout
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async function() {
            try {
                await signOut(auth);
                window.location.href = 'login.html';
            } catch (error) {
                console.error("Logout error:", error);
            }
        });
    }
});

// Function to show admin content
function showAdminContent() {
    const adminContent = document.querySelector('.admin-content');
    if (adminContent) {
        adminContent.style.display = 'block';
    }
}

// Make auth globally available
window.auth = auth; 