/* Reset y configuración base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #181818;
    margin: 0;
    font-family: 'Poppins', sans-serif;
    color: #fff;
    line-height: 1.6;
    overflow-x: hidden;
}

body.menu-open {
    overflow: hidden;
}

/* Header del panel de administración */
.admin-header {
    background: linear-gradient(135deg, #111 0%, #1a1a1a 100%);
    padding: 1.5rem 2rem;
    border-bottom: 2px solid #f8ecc1;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.admin-header h1 {
    color: #f8ecc1;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.admin-header h1 i {
    font-size: 1.6rem;
    color: #f8ecc1;
}

.admin-nav {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.admin-nav a {
    color: #f8ecc1;
    text-decoration: none;
    padding: 0.6rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    border: 1px solid transparent;
    white-space: nowrap;
}

.admin-nav a:hover, .admin-nav a.active {
    background: rgba(248, 236, 193, 0.15);
    border-color: rgba(248, 236, 193, 0.3);
    transform: translateY(-1px);
}

.admin-nav a i {
    font-size: 0.9rem;
}

/* Botón de menú hamburguesa */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #f8ecc1;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Contenedor principal */
.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    min-height: calc(100vh - 100px);
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(248, 236, 193, 0.2);
    border-radius: 12px;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #f8ecc1, #e6d4a3);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border-color: rgba(248, 236, 193, 0.4);
}

.stat-card i {
    font-size: 2.5rem;
    color: #f8ecc1;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.stat-card h3 {
    color: #f8ecc1;
    margin: 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
}

.stat-card p {
    color: #ccc;
    margin: 0;
    font-size: 0.95rem;
    font-weight: 500;
    opacity: 0.8;
}

/* Sección de solicitudes */
.requests-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(248, 236, 193, 0.2);
    border-radius: 12px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.requests-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #f8ecc1, #e6d4a3);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    color: #f8ecc1;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.section-header h2 i {
    font-size: 1.5rem;
    opacity: 0.9;
}

.filter-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-controls select, .filter-controls input {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(248, 236, 193, 0.3);
    border-radius: 8px;
    color: #fff;
    padding: 0.7rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    min-width: 150px;
}

.filter-controls select:hover, .filter-controls input:hover {
    border-color: rgba(248, 236, 193, 0.5);
}

.filter-controls select:focus, .filter-controls input:focus {
    outline: none;
    border-color: #f8ecc1;
}

/* Tabla de solicitudes */
.table-container {
    overflow-x: auto;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(248, 236, 193, 0.1);
    backdrop-filter: blur(10px);
    scrollbar-width: thin;
    scrollbar-color: rgba(248, 236, 193, 0.3) transparent;
}

.table-container::-webkit-scrollbar {
    height: 6px;
}

.table-container::-webkit-scrollbar-track {
    background: transparent;
}

.table-container::-webkit-scrollbar-thumb {
    background: rgba(248, 236, 193, 0.3);
    border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(248, 236, 193, 0.5);
}

.requests-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    min-width: 800px;
}

.requests-table th,
.requests-table td {
    padding: 1.2rem 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(248, 236, 193, 0.1);
}

.requests-table th {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.15) 0%, rgba(248, 236, 193, 0.08) 100%);
    color: #f8ecc1;
    font-weight: 600;
    font-size: 0.95rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.requests-table td {
    color: #ccc;
    transition: background-color 0.3s ease;
}

.requests-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.03);
}

/* Badges de Estado Mejorados */
.status-badge {
    padding: 0.4rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.status-badge:hover::before {
    left: 100%;
}

.status-pending {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
    color: #1a1a1a;
    border-color: rgba(255, 193, 7, 0.5);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.status-pending:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(255, 193, 7, 0.4);
}

.status-in-progress {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: #ffffff;
    border-color: rgba(33, 150, 243, 0.5);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.status-in-progress:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
}

.status-completed {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: #ffffff;
    border-color: rgba(76, 175, 80, 0.5);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.status-completed:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

.status-cancelled {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: #ffffff;
    border-color: rgba(244, 67, 54, 0.5);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.status-cancelled:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(244, 67, 54, 0.4);
}

/* Badges de Prioridad Mejorados */
.priority-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    border: 1px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.priority-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.priority-badge:hover::before {
    left: 100%;
}

.priority-low {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: #ffffff;
    border-color: rgba(76, 175, 80, 0.5);
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.priority-low:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 14px rgba(76, 175, 80, 0.4);
}

.priority-medium {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
    color: #1a1a1a;
    border-color: rgba(255, 193, 7, 0.5);
    box-shadow: 0 3px 10px rgba(255, 193, 7, 0.3);
}

.priority-medium:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 14px rgba(255, 193, 7, 0.4);
}

.priority-high {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: #ffffff;
    border-color: rgba(255, 152, 0, 0.5);
    box-shadow: 0 3px 10px rgba(255, 152, 0, 0.3);
}

.priority-high:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 14px rgba(255, 152, 0, 0.4);
}

.priority-urgent {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: #ffffff;
    border-color: rgba(244, 67, 54, 0.5);
    box-shadow: 0 3px 10px rgba(244, 67, 54, 0.3);
}

.priority-urgent:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 14px rgba(244, 67, 54, 0.4);
}

/* Botones de Acción Mejorados */
.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 50%, rgba(255, 255, 255, 0.01) 100%);
    border-radius: 20px;
    border: 2px solid transparent;
    background-clip: padding-box;
    backdrop-filter: blur(15px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    border-image: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05)) 1;
    position: relative;
    overflow: hidden;
}

.action-buttons::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 20px;
}

.action-buttons:hover {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 50%, rgba(255, 255, 255, 0.02) 100%);
    border-image: linear-gradient(145deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1)) 1;
    box-shadow: 
        0 16px 48px rgba(0, 0, 0, 0.4),
        0 8px 24px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.action-buttons:hover::before {
    opacity: 1;
}

.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #f8ecc1 0%, #e6d4a3 100%);
    color: #181818;
    border: 1px solid rgba(248, 236, 193, 0.3);
    box-shadow: 0 4px 12px rgba(248, 236, 193, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e6d4a3 0%, #d4c293 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(248, 236, 193, 0.3);
    border-color: rgba(248, 236, 193, 0.5);
}

.btn-secondary {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.1) 0%, rgba(248, 236, 193, 0.05) 100%);
    color: #f8ecc1;
    border: 1px solid rgba(248, 236, 193, 0.3);
    box-shadow: 0 4px 12px rgba(248, 236, 193, 0.1);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.2) 0%, rgba(248, 236, 193, 0.1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(248, 236, 193, 0.2);
    border-color: rgba(248, 236, 193, 0.5);
}

.btn-danger {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.15) 0%, rgba(244, 67, 54, 0.08) 100%);
    color: #ff6b6b;
    border: 1px solid rgba(244, 67, 54, 0.3);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.1);
}

.btn-danger:hover {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.25) 0%, rgba(244, 67, 54, 0.15) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.5);
}

/* Botón de solo icono para eliminar */
.btn-icon {
    padding: 0.6rem;
    min-width: 40px;
    justify-content: center;
    border-radius: 8px;
}

.btn-icon i {
    font-size: 0.9rem;
}

/* Estado vacío */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #ccc;
}

.empty-state i {
    font-size: 3rem;
    color: #f8ecc1;
    margin-bottom: 1rem;
}

/* Modales Mejorados */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
    animation: fadeIn 0.4s ease;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(8px);
}

.modal-content {
    background: linear-gradient(145deg, #1a1a1a 0%, #0f0f0f 50%, #1a1a1a 100%);
    border: 1px solid rgba(248, 236, 193, 0.2);
    border-radius: 20px;
    padding: 0;
    width: 95%;
    max-width: 650px;
    max-height: 85vh;
    overflow: hidden;
    position: relative;
    animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.8),
        0 0 0 1px rgba(248, 236, 193, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f8ecc1, #e6d4a3, #f8ecc1);
    border-radius: 20px 20px 0 0;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1.5rem 2rem;
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.05) 0%, rgba(248, 236, 193, 0.02) 100%);
    border-bottom: 1px solid rgba(248, 236, 193, 0.1);
    position: relative;
}

.modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 2rem;
    right: 2rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(248, 236, 193, 0.3), transparent);
}

.modal-header h3 {
    color: #f8ecc1;
    margin: 0;
    font-size: 1.6rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.modal-header h3 i {
    font-size: 1.4rem;
    color: #f8ecc1;
    opacity: 0.9;
}

.close-modal {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.1) 0%, rgba(248, 236, 193, 0.05) 100%);
    border: 1px solid rgba(248, 236, 193, 0.2);
    color: #f8ecc1;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.8rem;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    backdrop-filter: blur(10px);
}

.close-modal:hover {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.2) 0%, rgba(248, 236, 193, 0.1) 100%);
    transform: scale(1.05) rotate(90deg);
    box-shadow: 0 4px 12px rgba(248, 236, 193, 0.2);
}

.modal-body {
    color: #ccc;
    line-height: 1.7;
    padding: 2.5rem;
    max-height: 60vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(248, 236, 193, 0.3) transparent;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    border-radius: 0 0 20px 20px;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: transparent;
}

.modal-body::-webkit-scrollbar-thumb {
    background: rgba(248, 236, 193, 0.3);
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(248, 236, 193, 0.5);
}

.request-detail {
    margin-bottom: 1.5rem;
    padding: 1.2rem 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
    border-radius: 12px;
    border: 1px solid rgba(248, 236, 193, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    display: flex;
    align-items: center;
    min-height: 50px;
    backdrop-filter: blur(10px);
}

.request-detail::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(248, 236, 193, 0.1), transparent);
}

.request-detail:hover::before {
    background: linear-gradient(90deg, transparent, rgba(248, 236, 193, 0.3), transparent);
}

.request-detail strong {
    color: #f8ecc1;
    display: inline-block;
    width: 140px;
    margin-right: 1.5rem;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
    position: relative;
}

.request-detail strong::after {
    content: ':';
    color: rgba(248, 236, 193, 0.6);
    margin-left: 0.3rem;
}

/* Estilo especial para el mensaje */
.request-detail.message-detail {
    flex-direction: column;
    align-items: flex-start;
    margin-top: 2.5rem;
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.05) 0%, rgba(248, 236, 193, 0.02) 100%);
    border: 1px solid rgba(248, 236, 193, 0.15);
    border-radius: 16px;
    position: relative;
    backdrop-filter: blur(15px);
}

.request-detail.message-detail::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #f8ecc1, transparent);
}

.request-detail.message-detail strong {
    width: auto;
    margin-bottom: 1.5rem;
    font-size: 1rem;
    color: #f8ecc1;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.request-detail.message-detail strong::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 30px;
    height: 1px;
    background: rgba(248, 236, 193, 0.5);
}

.message-content {
    width: 100%;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    border: 1px solid rgba(248, 236, 193, 0.08);
    border-radius: 12px;
    line-height: 1.8;
    font-style: normal;
    color: #d0d0d0;
    position: relative;
    overflow: visible;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    font-size: 1rem;
    text-align: justify;
    word-wrap: break-word;
    margin-top: 1rem;
}

.message-content::before,
.message-content::after {
    display: none;
}

.message-content:hover {
    background: transparent;
    border-color: transparent;
    transform: none;
    box-shadow: none;
}

.modal-footer {
    margin-top: 0;
    padding: 1.5rem 2rem 2rem 2rem;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
    border-top: 1px solid rgba(248, 236, 193, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    flex-wrap: wrap;
    position: relative;
}

.modal-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2rem;
    right: 2rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(248, 236, 193, 0.2), transparent);
}

.btn-modal {
    padding: 0.9rem 1.8rem;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: inline-flex;
    align-items: center;
    gap: 0.6rem;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-modal::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-modal:hover::before {
    left: 100%;
}

.btn-modal-danger {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.15) 0%, rgba(244, 67, 54, 0.08) 100%);
    color: #ff6b6b;
    border: 1px solid rgba(244, 67, 54, 0.3);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.1);
}

.btn-modal-danger:hover {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.25) 0%, rgba(244, 67, 54, 0.15) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.5);
}

.btn-modal-secondary {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.08) 0%, rgba(248, 236, 193, 0.04) 100%);
    color: #f8ecc1;
    border: 1px solid rgba(248, 236, 193, 0.3);
    box-shadow: 0 4px 12px rgba(248, 236, 193, 0.1);
}

.btn-modal-secondary:hover {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.15) 0%, rgba(248, 236, 193, 0.08) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(248, 236, 193, 0.2);
    border-color: rgba(248, 236, 193, 0.5);
}

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Responsive Design - Desktop (1200px y superior) */
@media (min-width: 1200px) {
    .admin-container {
        padding: 2.5rem;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }
    
    .stat-card {
        padding: 2.5rem 2rem;
    }
    
    .stat-card i {
        font-size: 3rem;
    }
    
    .stat-card h3 {
        font-size: 2.5rem;
    }
}

/* Responsive Design - Laptop (768px - 1199px) */
@media (max-width: 1199px) and (min-width: 768px) {
    .admin-header {
        padding: 1.2rem 1.5rem;
    }
    
    .admin-header h1 {
        font-size: 1.6rem;
    }
    
    .admin-nav {
        gap: 0.8rem;
    }
    
    .admin-nav a {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .admin-container {
        padding: 1.5rem;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.2rem;
    }
    
    .requests-section {
        padding: 2rem;
    }
    
    .section-header {
        flex-direction: row;
        align-items: center;
    }
    
    .filter-controls {
        gap: 0.8rem;
    }
    
    .filter-controls select, .filter-controls input {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
        min-width: 140px;
    }
}

/* Responsive Design - Tablet (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
    .admin-header {
        padding: 1rem;
    }

    .header-container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .admin-header h1 {
        font-size: 1.4rem;
    }

    .admin-nav {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.6rem;
        padding: 0.8rem;
        background: rgba(255, 255, 255, 0.02);
        border-radius: 12px;
        border: 1px solid rgba(248, 236, 193, 0.1);
        backdrop-filter: blur(10px);
    }

    .admin-nav a {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
        flex: 1;
        min-width: 140px;
        justify-content: center;
        border-radius: 8px;
    }

    .admin-container {
        padding: 1.5rem;
    }

    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem 1rem;
    }

    .stat-card i {
        font-size: 2rem;
    }

    .stat-card h3 {
        font-size: 1.5rem;
    }

    .requests-section {
        padding: 1.5rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .filter-controls {
        flex-wrap: wrap;
        gap: 0.8rem;
    }

    .filter-controls select, .filter-controls input {
        flex: 1;
        min-width: 150px;
    }

    .requests-table {
        font-size: 0.85rem;
    }

    .requests-table th,
    .requests-table td {
        padding: 0.8rem 0.5rem;
    }

                .modal-content {
                margin: 5% auto;
                width: 95%;
                max-height: 90vh;
            }

            .modal-header {
                padding: 1.5rem 1.5rem 1rem 1.5rem;
            }

            .modal-body {
                padding: 1.5rem;
                max-height: 70vh;
            }

            .request-detail {
                padding: 0.5rem 0;
                min-height: 40px;
            }

            .request-detail strong {
                width: 100px;
                font-size: 0.85rem;
            }

            .message-detail {
                margin-top: 1.5rem;
                padding-top: 1rem;
            }

            .message-detail strong {
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }

            .message-content {
                font-size: 0.95rem;
            }

            .modal-footer {
                padding: 1rem 1.5rem 1.5rem 1.5rem;
            }
}

/* Responsive Design - Mobile (480px y menor) */
@media (max-width: 480px) {
    .admin-header {
        padding: 0.8rem;
    }

    .header-container {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .admin-header h1 {
        font-size: 1.3rem;
        text-align: left;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .admin-nav {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: rgba(24, 24, 24, 0.98);
        z-index: 1000;
        padding-top: 80px;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        gap: 1rem;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .mobile-menu-close {
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .close-menu-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(248, 236, 193, 0.3);
        color: #f8ecc1;
        padding: 0.8rem;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1.2rem;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .close-menu-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: #f8ecc1;
        transform: scale(1.1);
    }

    .admin-nav.active {
        display: flex;
    }

    .admin-nav a {
        padding: 1.2rem 2rem;
        font-size: 1.1rem;
        justify-content: center;
        border-radius: 12px;
        margin: 0.8rem 0;
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(248, 236, 193, 0.2);
        min-width: 250px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .admin-nav a:hover {
        background: rgba(255, 255, 255, 0.12);
        border-color: rgba(248, 236, 193, 0.4);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    }

    .admin-nav a i {
        font-size: 1.1rem;
        margin-right: 0.8rem;
    }

    .admin-container {
        padding: 1rem;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem 1rem;
    }

    .stat-card i {
        font-size: 2rem;
    }

    .stat-card h3 {
        font-size: 1.5rem;
    }

    .requests-section {
        padding: 1rem;
    }

    .section-header h2 {
        font-size: 1.4rem;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .filter-controls select, .filter-controls input {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
        min-width: auto;
    }

    .requests-table {
        font-size: 0.75rem;
        min-width: 600px;
    }

    .requests-table th,
    .requests-table td {
        padding: 0.5rem 0.3rem;
    }

    .table-container {
        overflow-x: auto;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(248, 236, 193, 0.1);
        backdrop-filter: blur(10px);
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.8rem;
        margin: 0.5rem 0;
    }
    
    .btn-action {
        min-width: 120px;
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
        margin: 0.2rem 0;
        border-radius: 12px;
        background: linear-gradient(145deg, #2a2a2a 0%, #1a1a1a 50%, #0f0f0f 100%);
        color: #ffffff;
        border: none !important;
        outline: none !important;
        box-shadow: 
            0 6px 20px rgba(0, 0, 0, 0.3),
            0 3px 10px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(15px);
        border-image: none !important;
    }
    
    .btn-view {
        background: linear-gradient(145deg, #3a5f9e 0%, #2d4a7a 50%, #1e3a5f 100%);
        color: #ffffff;
        border: none !important;
        outline: none !important;
        box-shadow: 
            0 6px 20px rgba(58, 95, 158, 0.3),
            0 3px 10px rgba(58, 95, 158, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    }
    
    .btn-delete {
        background: linear-gradient(145deg, #d32f2f 0%, #b71c1c 50%, #8e1a1a 100%);
        color: #ffffff;
        border: none !important;
        outline: none !important;
        box-shadow: 
            0 6px 20px rgba(211, 47, 47, 0.3),
            0 3px 10px rgba(211, 47, 47, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.75rem;
    }

                .modal-content {
                margin: 2% auto;
                width: 98%;
                max-height: 95vh;
                border-radius: 16px;
            }

            .modal-header {
                padding: 1.5rem 1.5rem 1rem 1.5rem;
                border-radius: 16px 16px 0 0;
            }

            .modal-header h3 {
                font-size: 1.3rem;
            }

            .modal-body {
                padding: 1.5rem;
                max-height: 75vh;
                border-radius: 0 0 16px 16px;
            }

            .request-detail {
                padding: 1rem 1.2rem;
                min-height: 40px;
                margin-bottom: 1rem;
                border-radius: 10px;
            }

            .request-detail strong {
                width: 80px;
                font-size: 0.85rem;
            }

            .message-detail {
                margin-top: 1.5rem;
                padding: 1.5rem;
                border-radius: 12px;
            }

            .message-detail strong {
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }

            .message-content {
                font-size: 0.9rem;
                padding: 1rem;
                border-radius: 8px;
            }

            .modal-footer {
                padding: 1rem 1.5rem 1.5rem 1.5rem;
            }

            .btn-modal {
                padding: 0.8rem 1.6rem;
                font-size: 0.9rem;
                border-radius: 10px;
            }

    .modal-footer {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-modal {
        justify-content: center;
    }
}

/* Mejoras para pantallas muy pequeñas */
@media (max-width: 360px) {
    .admin-header h1 {
        font-size: 1.1rem;
    }

    .mobile-menu-toggle {
        padding: 0.3rem;
    }

    .mobile-menu-toggle span {
        width: 20px;
        height: 2px;
    }

    .admin-nav a {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        min-width: 180px;
    }

    .stat-card {
        padding: 1.2rem 0.8rem;
    }

    .stat-card i {
        font-size: 1.8rem;
    }

    .stat-card h3 {
        font-size: 1.3rem;
    }

    .requests-table {
        font-size: 0.7rem;
        min-width: 500px;
    }

    .requests-table th,
    .requests-table td {
        padding: 0.4rem 0.2rem;
    }

    .status-badge, .priority-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    .modal-content {
        margin: 1% auto;
        width: 99%;
        max-height: 98vh;
    }

    .modal-body {
        padding: 1rem;
        max-height: 80vh;
    }

    .request-detail {
        padding: 0.8rem 1rem;
        margin-bottom: 0.8rem;
    }

    .request-detail strong {
        width: 60px;
        font-size: 0.75rem;
    }

    .message-detail {
        padding: 1rem;
        margin-top: 1rem;
    }

    .message-content {
        padding: 0.8rem;
        font-size: 0.85rem;
    }

    .btn-modal {
        padding: 0.6rem 1.2rem;
        font-size: 0.8rem;
    }
}

/* Mejoras para pantallas ultra-wide */
@media (min-width: 1600px) {
    .admin-container {
        max-width: 1600px;
        padding: 3rem;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 2.5rem;
    }
    
    .stat-card {
        padding: 3rem 2.5rem;
    }
    
    .stat-card i {
        font-size: 3.5rem;
    }
    
    .stat-card h3 {
        font-size: 3rem;
    }
    
    .requests-section {
        padding: 3rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
}

/* Mejoras para laptops (1024px - 1366px) */
@media (min-width: 1024px) and (max-width: 1366px) {
    .admin-container {
        max-width: 1200px;
        padding: 2rem;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
    
    .stat-card {
        padding: 2rem 1.5rem;
    }
    
    .stat-card i {
        font-size: 2.5rem;
    }
    
    .stat-card h3 {
        font-size: 2.2rem;
    }
    
    .requests-section {
        padding: 2rem;
    }
    
    .section-header h2 {
        font-size: 1.6rem;
    }
    
    .filter-controls {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .filter-controls select, .filter-controls input {
        min-width: 160px;
    }
    
    .btn-action {
        min-width: 100px;
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
    }
}

/* Mejoras para tablets en modo landscape (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .admin-header {
        padding: 1.2rem 1.5rem;
    }
    
    .admin-header h1 {
        font-size: 1.5rem;
    }
    
    .admin-nav a {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .admin-container {
        padding: 1.5rem;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .stat-card {
        padding: 1.8rem 1.2rem;
    }
    
    .stat-card i {
        font-size: 2.2rem;
    }
    
    .stat-card h3 {
        font-size: 1.8rem;
    }
    
    .requests-section {
        padding: 1.5rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .section-header h2 {
        font-size: 1.4rem;
    }
    
    .filter-controls {
        flex-wrap: wrap;
        gap: 0.8rem;
        width: 100%;
    }
    
    .filter-controls select, .filter-controls input {
        min-width: 140px;
        font-size: 0.85rem;
    }
    
    .requests-table {
        font-size: 0.9rem;
    }
    
    .requests-table th,
    .requests-table td {
        padding: 0.8rem 0.6rem;
    }
    
    .btn-action {
        min-width: 90px;
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
        margin: 0 0.3rem;
    }
    
    .action-buttons {
        gap: 0.6rem;
        padding: 0.6rem;
    }
}

/* Mejoras para orientación landscape en móviles */
@media (max-width: 767px) and (orientation: landscape) {
    .admin-header {
        flex-direction: row;
        text-align: left;
    }
    
    .admin-nav {
        flex-wrap: nowrap;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-header {
        flex-direction: row;
        align-items: center;
    }
    
    .filter-controls {
        flex-direction: row;
        align-items: center;
    }
}

/* Mejoras para tablets en modo portrait (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) and (orientation: portrait) {
    .admin-header {
        padding: 1rem 1.5rem;
    }
    
    .admin-header h1 {
        font-size: 1.4rem;
    }
    
    .admin-nav a {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    .admin-container {
        padding: 1rem;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1.5rem 1rem;
    }
    
    .stat-card i {
        font-size: 2rem;
    }
    
    .stat-card h3 {
        font-size: 1.6rem;
    }
    
    .requests-section {
        padding: 1.5rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .section-header h2 {
        font-size: 1.3rem;
    }
    
    .filter-controls {
        flex-wrap: wrap;
        gap: 0.6rem;
        width: 100%;
    }
    
    .filter-controls select, .filter-controls input {
        min-width: 120px;
        font-size: 0.8rem;
        padding: 0.6rem 0.8rem;
    }
    
    .requests-table {
        font-size: 0.85rem;
        min-width: 700px;
    }
    
    .requests-table th,
    .requests-table td {
        padding: 0.6rem 0.4rem;
    }
    
    .btn-action {
        min-width: 80px;
        padding: 0.5rem 0.8rem;
        font-size: 0.75rem;
        margin: 0 0.2rem;
    }
    
    .action-buttons {
        gap: 0.4rem;
        padding: 0.4rem;
    }
    
    .status-badge, .priority-badge {
        font-size: 0.75rem;
        padding: 0.3rem 0.6rem;
    }
}

/* Estilos para los selectores de status y priority mejorados */
.status-select, .priority-select {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(248, 236, 193, 0.4);
    border-radius: 8px;
    color: #f8ecc1;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.status-select::before, .priority-select::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(248, 236, 193, 0.1), transparent);
    transition: left 0.5s ease;
}

.status-select:hover::before, .priority-select:hover::before {
    left: 100%;
}

.status-select:hover, .priority-select:hover {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.2) 0%, rgba(248, 236, 193, 0.1) 100%);
    border-color: #f8ecc1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(248, 236, 193, 0.2);
}

.status-select:focus, .priority-select:focus {
    outline: none;
    border-color: #f8ecc1;
    box-shadow: 0 0 15px rgba(248, 236, 193, 0.3);
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.25) 0%, rgba(248, 236, 193, 0.15) 100%);
}

.status-select option, .priority-select option {
    background: #1a1a1a;
    color: #f8ecc1;
    padding: 0.5rem;
    border: none;
    font-weight: 500;
}

.status-select option:hover, .priority-select option:hover {
    background: rgba(248, 236, 193, 0.1);
}

/* Mejoras para móviles */
@media (max-width: 767px) {
    .status-select, .priority-select {
        font-size: 0.75rem;
        padding: 0.3rem 0.6rem;
        margin-left: 0.5rem;
    }
}

/* Estilos para los botones de acción mejorados */
/* Botones de acción modernos y elegantes */
.btn-action {
    background: linear-gradient(145deg, #2a2a2a 0%, #1a1a1a 50%, #0f0f0f 100%) !important;
    border: none !important;
    outline: none !important;
    color: #ffffff !important;
    cursor: pointer;
    padding: 0.8rem 1.5rem;
    border-radius: 16px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
    font-weight: 700;
    margin: 0 0.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.6rem;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 110px;
    justify-content: center;
    font-family: 'Poppins', sans-serif;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;
    backdrop-filter: blur(20px);
    border-image: none !important;
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 16px;
}

.btn-action::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.8s ease;
    border-radius: 16px;
}

.btn-action:hover::before {
    opacity: 1;
}

.btn-action:hover::after {
    left: 100%;
}

.btn-action:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 
        0 16px 48px rgba(0, 0, 0, 0.5),
        0 8px 24px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    border-image: linear-gradient(145deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1)) 1;
}

.btn-action:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.1s ease;
}

/* Botón Ver - Estilo azul moderno con efectos glassmorphism */
.btn-view {
    background: linear-gradient(145deg, #3a5f9e 0%, #2d4a7a 50%, #1e3a5f 100%) !important;
    color: #ffffff !important;
    box-shadow: 
        0 8px 32px rgba(58, 95, 158, 0.4),
        0 4px 16px rgba(58, 95, 158, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3) !important;
}

.btn-view:hover {
    background: linear-gradient(145deg, #4a6fb8 0%, #3a5f9e 50%, #2d4a7a 100%);
    border-image: linear-gradient(145deg, rgba(74, 111, 184, 0.8), rgba(58, 95, 158, 0.5)) 1;
    box-shadow: 
        0 16px 48px rgba(58, 95, 158, 0.6),
        0 8px 24px rgba(58, 95, 158, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.4);
    color: #ffffff;
}

.btn-view:active {
    background: linear-gradient(145deg, #2d4a7a 0%, #1e3a5f 50%, #152a47 100%);
    transform: translateY(-2px) scale(1.02);
}

/* Botón Eliminar - Estilo rojo moderno con efectos glassmorphism */
.btn-delete {
    background: linear-gradient(145deg, #d32f2f 0%, #b71c1c 50%, #8e1a1a 100%) !important;
    color: #ffffff !important;
    box-shadow: 
        0 8px 32px rgba(211, 47, 47, 0.4),
        0 4px 16px rgba(211, 47, 47, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3) !important;
}

.btn-delete:hover {
    background: linear-gradient(145deg, #f44336 0%, #d32f2f 50%, #b71c1c 100%);
    border-image: linear-gradient(145deg, rgba(244, 67, 54, 0.8), rgba(211, 47, 47, 0.5)) 1;
    box-shadow: 
        0 16px 48px rgba(211, 47, 47, 0.6),
        0 8px 24px rgba(211, 47, 47, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.4);
    color: #ffffff;
}

.btn-delete:active {
    background: linear-gradient(145deg, #b71c1c 0%, #8e1a1a 50%, #6a1b1b 100%);
    transform: translateY(-2px) scale(1.02);
}

/* Iconos de los botones modernos */
.btn-action i {
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.btn-action:hover i {
    transform: scale(1.15) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
}

.btn-view:hover i {
    color: #a8c6ff;
    text-shadow: 0 0 10px rgba(168, 198, 255, 0.5);
}

.btn-delete:hover i {
    color: #ffcdd2;
    text-shadow: 0 0 10px rgba(255, 205, 210, 0.5);
}

/* Efectos de animación modernos */
.btn-delete:hover {
    animation: pulse-danger 2s infinite;
}

@keyframes pulse-danger {
    0% {
        box-shadow: 
            0 16px 48px rgba(211, 47, 47, 0.6),
            0 8px 24px rgba(211, 47, 47, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.4);
    }
    50% {
        box-shadow: 
            0 16px 48px rgba(211, 47, 47, 0.8),
            0 8px 24px rgba(211, 47, 47, 0.6),
            inset 0 1px 0 rgba(255, 255, 255, 0.4),
            inset 0 -1px 0 rgba(0, 0, 0, 0.5),
            0 0 30px rgba(211, 47, 47, 0.4);
    }
    100% {
        box-shadow: 
            0 16px 48px rgba(211, 47, 47, 0.6),
            0 8px 24px rgba(211, 47, 47, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.4);
    }
}

/* Efecto de brillo para botón ver */
.btn-view:hover {
    animation: glow-blue 3s ease-in-out infinite alternate;
}

@keyframes glow-blue {
    0% {
        box-shadow: 
            0 16px 48px rgba(58, 95, 158, 0.6),
            0 8px 24px rgba(58, 95, 158, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.4);
    }
    100% {
        box-shadow: 
            0 16px 48px rgba(58, 95, 158, 0.8),
            0 8px 24px rgba(58, 95, 158, 0.6),
            inset 0 1px 0 rgba(255, 255, 255, 0.4),
            inset 0 -1px 0 rgba(0, 0, 0, 0.5),
            0 0 25px rgba(58, 95, 158, 0.3);
    }
}

/* Estados adicionales para los botones */
.btn-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-action:disabled:hover {
    transform: none;
    box-shadow: none;
    animation: none;
}

.btn-action.loading {
    position: relative;
    color: transparent;
}

.btn-action.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Efectos de focus modernos para accesibilidad */
.btn-action:focus {
    outline: none;
    box-shadow: 
        0 0 0 4px rgba(255, 255, 255, 0.2),
        0 16px 48px rgba(0, 0, 0, 0.5),
        0 8px 24px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.btn-view:focus {
    box-shadow: 
        0 0 0 4px rgba(58, 95, 158, 0.4),
        0 16px 48px rgba(58, 95, 158, 0.6),
        0 8px 24px rgba(58, 95, 158, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.4);
}

.btn-delete:focus {
    box-shadow: 
        0 0 0 4px rgba(211, 47, 47, 0.4),
        0 16px 48px rgba(211, 47, 47, 0.6),
        0 8px 24px rgba(211, 47, 47, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.4);
}

/* Estilos para el botón de limpiar filtros mejorado */
.btn-clear-filters {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.15) 0%, rgba(255, 107, 107, 0.08) 100%);
    border: 1px solid rgba(255, 107, 107, 0.4);
    color: #ff6b6b;
    cursor: pointer;
    padding: 0.7rem 1.2rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.2);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.btn-clear-filters::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-clear-filters:hover::before {
    left: 100%;
}

.btn-clear-filters:hover {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.25) 0%, rgba(255, 107, 107, 0.15) 100%);
    border-color: #ff6b6b;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 107, 107, 0.3);
    color: #ff6b6b;
}

.btn-clear-filters i {
    font-size: 0.9rem;
    font-weight: 600;
} 

/* Estilos para las notificaciones */
.notification-content {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 0.9rem;
}

.notification-content i {
    font-size: 1.1rem;
    font-weight: 600;
}

.success-notification {
    animation: slideInRight 0.5s ease;
}

.error-notification {
    animation: slideInRight 0.5s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(400px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mejoras adicionales para la tabla */
.requests-table tbody tr {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.requests-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.1) 0%, rgba(248, 236, 193, 0.05) 100%);
    border-left-color: #f8ecc1;
    transform: translateX(5px);
}

/* Mejoras para los filtros */
.filter-controls select, .filter-controls input {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(248, 236, 193, 0.4);
    border-radius: 8px;
    color: #fff;
    padding: 0.8rem 1.2rem;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 180px;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.filter-controls select:hover, .filter-controls input:hover {
    border-color: rgba(248, 236, 193, 0.6);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(248, 236, 193, 0.2);
}

.filter-controls select:focus, .filter-controls input:focus {
    outline: none;
    border-color: #f8ecc1;
    box-shadow: 0 0 15px rgba(248, 236, 193, 0.3);
    background: linear-gradient(135deg, rgba(248, 236, 193, 0.15) 0%, rgba(248, 236, 193, 0.08) 100%);
}

.filter-controls select option {
    background: #1a1a1a;
    color: #f8ecc1;
    padding: 0.5rem;
    font-weight: 500;
} 