// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-app.js";
import { getFirestore, collection, addDoc, serverTimestamp } from "https://www.gstatic.com/firebasejs/12.0.0/firebase-firestore.js";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyA0tjUO8tJKGgDJFTWpoMKgQQqsdHYDeBE",
  authDomain: "print-design-pd.firebaseapp.com",
  projectId: "print-design-pd",
  storageBucket: "print-design-pd.firebasestorage.app",
  messagingSenderId: "678212447286",
  appId: "1:678212447286:web:81c6c1f16ebd599d68bc97",
  measurementId: "G-E98ZKDHSKK"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Handle contact form submission
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const contactData = {
                nombre: formData.get('nombre'),
                telefono: formData.get('telefono'),
                email: formData.get('email'),
                mensaje: formData.get('mensaje'),
                timestamp: serverTimestamp(),
                status: 'pending',
                priority: 'medium'
            };
            
            // Show loading indicator
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            submitBtn.disabled = true;
            
            try {
                console.log("Sending contact data:", contactData);
                // Add document to Firestore
                const docRef = await addDoc(collection(db, "contactos"), contactData);
                console.log("Document written with ID: ", docRef.id);
                
                // Show success message
                showSuccessMessage();
                
                // Reset form
                this.reset();
                
            } catch (error) {
                console.error("Error adding document: ", error);
                alert('Error al enviar el mensaje. Por favor, inténtalo de nuevo.');
            } finally {
                // Restore button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });
    }
});

// Function to show success message
function showSuccessMessage() {
    const successMessage = document.getElementById('success-message');
    if (successMessage) {
        successMessage.style.display = 'flex';
    }
}

// Function to hide success message
function hideSuccessMessage() {
    const successMessage = document.getElementById('success-message');
    if (successMessage) {
        successMessage.style.display = 'none';
    }
}

// Close message when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const successMessage = document.getElementById('success-message');
    if (successMessage) {
        successMessage.addEventListener('click', function(e) {
            if (e.target === this) {
                hideSuccessMessage();
            }
        });
    }
});

// Make functions globally available
window.showSuccessMessage = showSuccessMessage;
window.hideSuccessMessage = hideSuccessMessage; 